package com.example.voicemessageapp.data

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
interface MessageDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: Message): Long

    @Update
    suspend fun updateMessage(message: Message)

    @Delete
    suspend fun deleteMessage(message: Message)

    @Query("DELETE FROM messages WHERE id = :messageId")
    suspend fun deleteMessageById(messageId: Long)

    @Query("SELECT * FROM messages WHERE id = :messageId")
    fun getMessageById(messageId: Long): Flow<Message?>

    @Query("SELECT * FROM messages ORDER BY timestamp DESC")
    fun getAllMessages(): Flow<List<Message>>

    @Query("SELECT * FROM messages WHERE contactId = :contactId ORDER BY timestamp DESC")
    fun getMessagesByContact(contactId: String): Flow<List<Message>>

    @Query("SELECT * FROM messages WHERE textContent LIKE :query OR contactName LIKE :query ORDER BY timestamp DESC")
    fun searchMessages(query: String): Flow<List<Message>>

    @Query("DELETE FROM messages")
    suspend fun deleteAllMessages()
}
