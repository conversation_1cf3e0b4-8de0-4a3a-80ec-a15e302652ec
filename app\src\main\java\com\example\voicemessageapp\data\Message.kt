package com.example.voicemessageapp.data

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "messages")
data class Message(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val contactId: String?, // ID from system contacts, or phone number if not in contacts
    val contactName: String?, // Display name for the contact
    val voiceFilePath: String?,
    val textContent: String?,
    val timestamp: Long,
    val isSentByUser: Boolean, // To distinguish between user's messages and received messages (if applicable)
    val isRead: Boolean = false
)
