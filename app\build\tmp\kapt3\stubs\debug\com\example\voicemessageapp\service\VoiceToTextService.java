package com.example.voicemessageapp.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0013\u001a\u00020\u0014J\u0006\u0010\u0015\u001a\u00020\u0014J\u000e\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0018J\u000e\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u0007J\u000e\u0010\u001b\u001a\u00020\u00142\u0006\u0010\u001c\u001a\u00020\u0007J\u0006\u0010\u001d\u001a\u00020\u0014R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0019\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/example/voicemessageapp/service/VoiceToTextService;", "", "()V", "_connectionStatus", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/voicemessageapp/service/SocketStatus;", "_textResult", "", "client", "Lokhttp3/OkHttpClient;", "connectionStatus", "Lkotlinx/coroutines/flow/StateFlow;", "getConnectionStatus", "()Lkotlinx/coroutines/flow/StateFlow;", "textResult", "getTextResult", "webSocket", "Lokhttp3/WebSocket;", "webSocketUrl", "connect", "", "disconnect", "sendAudio", "audioData", "Lokio/ByteString;", "sendText", "textData", "setWebSocketUrl", "url", "shutdown", "app_debug"})
public final class VoiceToTextService {
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.Nullable()
    private okhttp3.WebSocket webSocket;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _textResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> textResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.voicemessageapp.service.SocketStatus> _connectionStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.voicemessageapp.service.SocketStatus> connectionStatus = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String webSocketUrl = "";
    
    public VoiceToTextService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getTextResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.voicemessageapp.service.SocketStatus> getConnectionStatus() {
        return null;
    }
    
    public final void setWebSocketUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
    }
    
    public final void connect() {
    }
    
    public final void sendAudio(@org.jetbrains.annotations.NotNull()
    okio.ByteString audioData) {
    }
    
    public final void sendText(@org.jetbrains.annotations.NotNull()
    java.lang.String textData) {
    }
    
    public final void disconnect() {
    }
    
    public final void shutdown() {
    }
}