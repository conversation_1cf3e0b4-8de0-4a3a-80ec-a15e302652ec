package com.example.voicemessageapp.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.voicemessageapp.data.AppDatabase
import com.example.voicemessageapp.data.Message
import com.example.voicemessageapp.service.VoiceToTextService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class MainViewModel(application: Application) : AndroidViewModel(application) {

    private val messageDao = AppDatabase.getDatabase(application).messageDao()
    // private val voiceToTextService = VoiceToTextService() // Initialize if needed directly

    val allMessages: Flow<List<Message>> = messageDao.getAllMessages()

    private val _searchText = MutableStateFlow("")
    val searchText: StateFlow<String> = _searchText

    // Example: Placeholder for search results, can be refined
    private val _searchResults = MutableStateFlow<List<Message>>(emptyList())
    val searchResults: StateFlow<List<Message>> = _searchResults

    // Example: For handling a single selected message or for detail view
    private val _selectedMessage = MutableStateFlow<Message?>(null)
    val selectedMessage: StateFlow<Message?> = _selectedMessage

    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording

    // TODO: Integrate with actual audio recording logic
    // For now, these are placeholders to be called from the UI
    fun startRecording() {
        // Actual recording start logic will go here
        // e.g., initialize MediaRecorder, define output file
        _isRecording.value = true
        // Log or update UI state
        android.util.Log.d("MainViewModel", "Recording started...")
    }

    fun stopRecordingAndProcess() {
        // Actual recording stop logic will go here
        // e.g., stop MediaRecorder, get the file path
        _isRecording.value = false
        android.util.Log.d("MainViewModel", "Recording stopped.")
        // TODO: Process the recorded audio:
        // 1. Get the audio file.
        // 2. Send to VoiceToTextService (if WebSocket is connected).
        // 3. Save message (with voice file path and/or text) to Room.
        // val voiceFilePath = "path/to/recorded/audio.3gp" // example
        // val textContent = "Voice converted to text" // example from service
        // val newMessage = Message(
        //     contactId = null, // Or some default/current contact
        //     contactName = null,
        //     voiceFilePath = voiceFilePath,
        //     textContent = textContent,
        //     timestamp = System.currentTimeMillis(),
        //     isSentByUser = true
        // )
        // insertMessage(newMessage)
    }

    fun insertMessage(message: Message) {
        viewModelScope.launch {
            messageDao.insertMessage(message)
        }
    }

    fun deleteMessage(message: Message) {
        viewModelScope.launch {
            messageDao.deleteMessage(message)
            // Optionally, delete the associated voice file from storage here
        }
    }

    fun searchMessages(query: String) {
        _searchText.value = query
        viewModelScope.launch {
            // This is a simple search, refine as needed.
            // The DAO searchMessages function expects a pattern like "%query%"
            messageDao.searchMessages("%$query%").collect { messages ->
                _searchResults.value = messages
            }
        }
    }

    fun getMessageById(id: Long) {
        viewModelScope.launch {
            messageDao.getMessageById(id).collect { message ->
                _selectedMessage.value = message
            }
        }
    }

    // TODO: Add methods for voice recording, WebSocket interaction, contact fetching etc.
    // For example:
    // fun connectWebSocket(url: String) { voiceToTextService.setWebSocketUrl(url); voiceToTextService.connect() }
    // fun disconnectWebSocket() { voiceToTextService.disconnect() }

    override fun onCleared() {
        super.onCleared()
        // voiceToTextService.shutdown() // Clean up WebSocket service if initialized
    }
}
