package com.example.voicemessageapp.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.voicemessageapp.R
import com.example.voicemessageapp.ui.theme.VoiceMessageAppTheme
import com.example.voicemessageapp.ui.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavController,
    viewModel: MainViewModel = viewModel()
) {
    // Observe messages from ViewModel
    val messages by viewModel.allMessages.collectAsState(initial = emptyList())
    val isRecording by viewModel.isRecording.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(title = { Text(stringResource(id = R.string.app_name)) })
        },
        floatingActionButton = {
            FloatingActionButton(onClick = {
                if (isRecording) {
                    viewModel.stopRecordingAndProcess()
                } else {
                    // TODO: Request RECORD_AUDIO permission before starting
                    viewModel.startRecording()
                }
            }) {
                Icon(
                    imageVector = if (isRecording) Icons.Filled.Mic else Icons.Filled.Add, // Mic for recording, Add for not recording
                    contentDescription = if (isRecording) stringResource(R.string.stop_recording) else stringResource(R.string.add_voice_message)
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            if (messages.isEmpty()) {
                Text(text = "No messages yet. Start a conversation!")
            } else {
                // Later, replace this with a LazyColumn to display messages
                Text(text = "Messages count: ${messages.size}")
                // Example: Display first message's text content if available
                messages.firstOrNull()?.textContent?.let {
                    Text(text = "First message: $it")
                }
            }
            // TODO: Add UI elements for recording, sending messages, contacts list, etc.
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    VoiceMessageAppTheme {
        // In a real preview, you might want to provide a mock NavController and ViewModel
        MainScreen(navController = rememberNavController())
    }
}
