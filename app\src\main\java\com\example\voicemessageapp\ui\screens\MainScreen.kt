package com.example.voicemessageapp.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.voicemessageapp.R
import com.example.voicemessageapp.ui.theme.VoiceMessageAppTheme
import com.example.voicemessageapp.ui.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavController,
    viewModel: MainViewModel = viewModel(),
    requestAudioPermission: () -> Unit,
    hasRecordAudioPermission: Boolean
) {
    val messages by viewModel.allMessages.collectAsState(initial = emptyList())
    val isRecording by viewModel.isRecording.collectAsState()
    
    // 更新 ViewModel 中的权限状态
    LaunchedEffect(hasRecordAudioPermission) {
        viewModel.updateAudioPermission(hasRecordAudioPermission)
    }

    Scaffold(
        topBar = {
            TopAppBar(title = { Text(stringResource(id = R.string.app_name)) })
        },
        floatingActionButton = {
            FloatingActionButton(onClick = {
                if (isRecording) {
                    viewModel.stopRecordingAndProcess()
                } else {
                    if (hasRecordAudioPermission) {
                        viewModel.startRecording()
                    } else {
                        requestAudioPermission()
                    }
                }
            }) {
                Icon(
                    imageVector = if (isRecording) Icons.Filled.Mic else Icons.Filled.Add,
                    contentDescription = if (isRecording) stringResource(R.string.stop_recording) else stringResource(R.string.add_voice_message)
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            if (messages.isEmpty()) {
                Text(text = "No messages yet. Start a conversation!")
            } else {
                Text(text = "Messages count: ${messages.size}")
                messages.firstOrNull()?.textContent?.let {
                    Text(text = "First message: $it")
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    VoiceMessageAppTheme {
        MainScreen(
            navController = rememberNavController(),
            requestAudioPermission = {},
            hasRecordAudioPermission = false
        )
    }
}
