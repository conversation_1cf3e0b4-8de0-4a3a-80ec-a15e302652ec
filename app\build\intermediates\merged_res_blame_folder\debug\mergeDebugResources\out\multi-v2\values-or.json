{"logs": [{"outputFile": "com.example.voicemessageapp-mergeDebugResources-54:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0d0bdf927d9c583a8851b6fb66b6b215\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,310,413,518,619,721,8472", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "203,305,408,513,614,716,835,8568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d122e06e3bdf3c58781e850453f00f35\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,995,1077,1147,1222,1299,1375,1458,1525", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,990,1072,1142,1217,1294,1370,1453,1520,1639"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,937,1024,1116,1216,1302,1379,7839,7927,8014,8092,8174,8244,8319,8396,8573,8656,8723", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "932,1019,1111,1211,1297,1374,1472,7922,8009,8087,8169,8239,8314,8391,8467,8651,8718,8837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4bd9d6162cd50d8ff84acaffca5e2fb2\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8842,8927", "endColumns": "84,87", "endOffsets": "8922,9010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee7d57914d6cf982e5bf93d1be98a36a\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4763,4857,4948,5053,5133,5218,5319,5425,5518,5619,5706,5814,5913,6016,6140,6220,6323", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4758,4852,4943,5048,5128,5213,5314,5420,5513,5614,5701,5809,5908,6011,6135,6215,6318,6412"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1477,1599,1716,1829,1948,2042,2142,2259,2402,2528,2679,2764,2869,2965,3060,3176,3306,3416,3559,3697,3828,4020,4146,4275,4410,4540,4637,4733,4850,4972,5077,5182,5285,5427,5577,5684,5793,5868,5972,6074,6185,6279,6370,6475,6555,6640,6741,6847,6940,7041,7128,7236,7335,7438,7562,7642,7745", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "1594,1711,1824,1943,2037,2137,2254,2397,2523,2674,2759,2864,2960,3055,3171,3301,3411,3554,3692,3823,4015,4141,4270,4405,4535,4632,4728,4845,4967,5072,5177,5280,5422,5572,5679,5788,5863,5967,6069,6180,6274,6365,6470,6550,6635,6736,6842,6935,7036,7123,7231,7330,7433,7557,7637,7740,7834"}}]}]}