1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.voicemessageapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_CONTACTS" />
13-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:7:22-69
14
15    <permission
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.voicemessageapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.voicemessageapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Optional: if you plan to save audio files to external storage -->
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
20    <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" /> -->
21    <application
21-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:11:5-33:19
22        android:name="com.example.voicemessageapp.VoiceMessageApp"
22-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:12:9-40
23        android:allowBackup="true"
23-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:13:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:14:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:15:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:16:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:17:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:18:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:19:9-35
33        android:theme="@style/Theme.VoiceMessageApp" >
33-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:20:9-53
34        <activity
34-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:22:9-32:20
35            android:name="com.example.voicemessageapp.MainActivity"
35-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:23:13-41
36            android:exported="true"
36-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:24:13-36
37            android:label="@string/app_name"
37-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:25:13-45
38            android:theme="@style/Theme.VoiceMessageApp" >
38-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:26:13-57
39            <intent-filter>
39-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:27:13-31:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:28:17-69
40-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:28:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:30:17-77
42-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:30:27-74
43            </intent-filter>
44        </activity>
45
46        <service
46-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
47            android:name="androidx.room.MultiInstanceInvalidationService"
47-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
48            android:directBootAware="true"
48-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
49            android:exported="false" />
49-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
50
51        <activity
51-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
52            android:name="androidx.compose.ui.tooling.PreviewActivity"
52-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
53            android:exported="true" />
53-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
54        <activity
54-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
55            android:name="androidx.activity.ComponentActivity"
55-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
56            android:exported="true" />
56-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
57
58        <provider
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
59            android:name="androidx.startup.InitializationProvider"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
60            android:authorities="com.example.voicemessageapp.androidx-startup"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
61            android:exported="false" >
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
62            <meta-data
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.emoji2.text.EmojiCompatInitializer"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
64                android:value="androidx.startup" />
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
66-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
67                android:value="androidx.startup" />
67-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
70                android:value="androidx.startup" />
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
71        </provider>
72
73        <receiver
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
74            android:name="androidx.profileinstaller.ProfileInstallReceiver"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
75            android:directBootAware="false"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
76            android:enabled="true"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
77            android:exported="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
78            android:permission="android.permission.DUMP" >
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
83                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
86                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
89                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
90            </intent-filter>
91        </receiver>
92    </application>
93
94</manifest>
