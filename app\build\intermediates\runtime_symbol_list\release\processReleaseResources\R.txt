int attr action 0x7f010000
int attr alpha 0x7f010001
int attr argType 0x7f010002
int attr data 0x7f010003
int attr dataPattern 0x7f010004
int attr destination 0x7f010005
int attr enterAnim 0x7f010006
int attr exitAnim 0x7f010007
int attr font 0x7f010008
int attr fontProviderAuthority 0x7f010009
int attr fontProviderCerts 0x7f01000a
int attr fontProviderFetchStrategy 0x7f01000b
int attr fontProviderFetchTimeout 0x7f01000c
int attr fontProviderPackage 0x7f01000d
int attr fontProviderQuery 0x7f01000e
int attr fontProviderSystemFontFamily 0x7f01000f
int attr fontStyle 0x7f010010
int attr fontVariationSettings 0x7f010011
int attr fontWeight 0x7f010012
int attr graph 0x7f010013
int attr lStar 0x7f010014
int attr launchSingleTop 0x7f010015
int attr mimeType 0x7f010016
int attr navGraph 0x7f010017
int attr nestedScrollViewStyle 0x7f010018
int attr nullable 0x7f010019
int attr popEnterAnim 0x7f01001a
int attr popExitAnim 0x7f01001b
int attr popUpTo 0x7f01001c
int attr popUpToInclusive 0x7f01001d
int attr popUpToSaveState 0x7f01001e
int attr queryPatterns 0x7f01001f
int attr restoreState 0x7f010020
int attr route 0x7f010021
int attr shortcutMatchRequired 0x7f010022
int attr startDestination 0x7f010023
int attr targetPackage 0x7f010024
int attr ttcIndex 0x7f010025
int attr uri 0x7f010026
int color androidx_core_ripple_material_light 0x7f020000
int color androidx_core_secondary_text_default_material_light 0x7f020001
int color black 0x7f020002
int color call_notification_answer_color 0x7f020003
int color call_notification_decline_color 0x7f020004
int color notification_action_color_filter 0x7f020005
int color notification_icon_bg_color 0x7f020006
int color purple_200 0x7f020007
int color purple_500 0x7f020008
int color purple_700 0x7f020009
int color teal_200 0x7f02000a
int color teal_700 0x7f02000b
int color vector_tint_color 0x7f02000c
int color vector_tint_theme_color 0x7f02000d
int color white 0x7f02000e
int dimen compat_button_inset_horizontal_material 0x7f030000
int dimen compat_button_inset_vertical_material 0x7f030001
int dimen compat_button_padding_horizontal_material 0x7f030002
int dimen compat_button_padding_vertical_material 0x7f030003
int dimen compat_control_corner_material 0x7f030004
int dimen compat_notification_large_icon_max_height 0x7f030005
int dimen compat_notification_large_icon_max_width 0x7f030006
int dimen notification_action_icon_size 0x7f030007
int dimen notification_action_text_size 0x7f030008
int dimen notification_big_circle_margin 0x7f030009
int dimen notification_content_margin_start 0x7f03000a
int dimen notification_large_icon_height 0x7f03000b
int dimen notification_large_icon_width 0x7f03000c
int dimen notification_main_column_padding_top 0x7f03000d
int dimen notification_media_narrow_margin 0x7f03000e
int dimen notification_right_icon_size 0x7f03000f
int dimen notification_right_side_padding_top 0x7f030010
int dimen notification_small_icon_background_padding 0x7f030011
int dimen notification_small_icon_size_as_large 0x7f030012
int dimen notification_subtext_size 0x7f030013
int dimen notification_top_pad 0x7f030014
int dimen notification_top_pad_large_text 0x7f030015
int drawable ic_call_answer 0x7f040001
int drawable ic_call_answer_low 0x7f040002
int drawable ic_call_answer_video 0x7f040003
int drawable ic_call_answer_video_low 0x7f040004
int drawable ic_call_decline 0x7f040005
int drawable ic_call_decline_low 0x7f040006
int drawable ic_launcher_background 0x7f040007
int drawable ic_launcher_foreground 0x7f040008
int drawable notification_action_background 0x7f040009
int drawable notification_bg 0x7f04000a
int drawable notification_bg_low 0x7f04000b
int drawable notification_bg_low_normal 0x7f04000c
int drawable notification_bg_low_pressed 0x7f04000d
int drawable notification_bg_normal 0x7f04000e
int drawable notification_bg_normal_pressed 0x7f04000f
int drawable notification_icon_background 0x7f040010
int drawable notification_oversize_large_icon_bg 0x7f040011
int drawable notification_template_icon_bg 0x7f040012
int drawable notification_template_icon_low_bg 0x7f040013
int drawable notification_tile_bg 0x7f040014
int drawable notify_panel_notification_icon_bg 0x7f040015
int id accessibility_action_clickable_span 0x7f050000
int id accessibility_custom_action_0 0x7f050001
int id accessibility_custom_action_1 0x7f050002
int id accessibility_custom_action_10 0x7f050003
int id accessibility_custom_action_11 0x7f050004
int id accessibility_custom_action_12 0x7f050005
int id accessibility_custom_action_13 0x7f050006
int id accessibility_custom_action_14 0x7f050007
int id accessibility_custom_action_15 0x7f050008
int id accessibility_custom_action_16 0x7f050009
int id accessibility_custom_action_17 0x7f05000a
int id accessibility_custom_action_18 0x7f05000b
int id accessibility_custom_action_19 0x7f05000c
int id accessibility_custom_action_2 0x7f05000d
int id accessibility_custom_action_20 0x7f05000e
int id accessibility_custom_action_21 0x7f05000f
int id accessibility_custom_action_22 0x7f050010
int id accessibility_custom_action_23 0x7f050011
int id accessibility_custom_action_24 0x7f050012
int id accessibility_custom_action_25 0x7f050013
int id accessibility_custom_action_26 0x7f050014
int id accessibility_custom_action_27 0x7f050015
int id accessibility_custom_action_28 0x7f050016
int id accessibility_custom_action_29 0x7f050017
int id accessibility_custom_action_3 0x7f050018
int id accessibility_custom_action_30 0x7f050019
int id accessibility_custom_action_31 0x7f05001a
int id accessibility_custom_action_4 0x7f05001b
int id accessibility_custom_action_5 0x7f05001c
int id accessibility_custom_action_6 0x7f05001d
int id accessibility_custom_action_7 0x7f05001e
int id accessibility_custom_action_8 0x7f05001f
int id accessibility_custom_action_9 0x7f050020
int id action_container 0x7f050021
int id action_divider 0x7f050022
int id action_image 0x7f050023
int id action_text 0x7f050024
int id actions 0x7f050025
int id androidx_compose_ui_view_composition_context 0x7f050026
int id async 0x7f050027
int id blocking 0x7f050028
int id chronometer 0x7f050029
int id compose_view_saveable_id_tag 0x7f05002a
int id consume_window_insets_tag 0x7f05002b
int id dialog_button 0x7f05002c
int id edit_text_id 0x7f05002d
int id forever 0x7f05002e
int id hide_graphics_layer_in_inspector_tag 0x7f05002f
int id hide_ime_id 0x7f050030
int id hide_in_inspector_tag 0x7f050031
int id icon 0x7f050032
int id icon_group 0x7f050033
int id info 0x7f050034
int id inspection_slot_table_set 0x7f050035
int id is_pooling_container_tag 0x7f050036
int id italic 0x7f050037
int id line1 0x7f050038
int id line3 0x7f050039
int id nav_controller_view_tag 0x7f05003a
int id normal 0x7f05003b
int id notification_background 0x7f05003c
int id notification_main_column 0x7f05003d
int id notification_main_column_container 0x7f05003e
int id pooling_container_listener_holder_tag 0x7f05003f
int id report_drawn 0x7f050040
int id right_icon 0x7f050041
int id right_side 0x7f050042
int id tag_accessibility_actions 0x7f050043
int id tag_accessibility_clickable_spans 0x7f050044
int id tag_accessibility_heading 0x7f050045
int id tag_accessibility_pane_title 0x7f050046
int id tag_on_apply_window_listener 0x7f050047
int id tag_on_receive_content_listener 0x7f050048
int id tag_on_receive_content_mime_types 0x7f050049
int id tag_screen_reader_focusable 0x7f05004a
int id tag_state_description 0x7f05004b
int id tag_transition_group 0x7f05004c
int id tag_unhandled_key_event_manager 0x7f05004d
int id tag_unhandled_key_listeners 0x7f05004e
int id tag_window_insets_animation_callback 0x7f05004f
int id text 0x7f050050
int id text2 0x7f050051
int id time 0x7f050052
int id title 0x7f050053
int id view_tree_lifecycle_owner 0x7f050054
int id view_tree_on_back_pressed_dispatcher_owner 0x7f050055
int id view_tree_saved_state_registry_owner 0x7f050056
int id view_tree_view_model_store_owner 0x7f050057
int id wrapped_composition_tag 0x7f050058
int integer m3c_window_layout_in_display_cutout_mode 0x7f060000
int integer status_bar_notification_info_maxnum 0x7f060001
int layout custom_dialog 0x7f070000
int layout ime_base_split_test_activity 0x7f070001
int layout ime_secondary_split_test_activity 0x7f070002
int layout notification_action 0x7f070003
int layout notification_action_tombstone 0x7f070004
int layout notification_template_custom_big 0x7f070005
int layout notification_template_icon_group 0x7f070006
int layout notification_template_part_chronometer 0x7f070007
int layout notification_template_part_time 0x7f070008
int mipmap ic_launcher 0x7f080000
int mipmap ic_launcher_round 0x7f080001
int string add_voice_message 0x7f090000
int string androidx_startup 0x7f090001
int string app_name 0x7f090002
int string call_notification_answer_action 0x7f090003
int string call_notification_answer_video_action 0x7f090004
int string call_notification_decline_action 0x7f090005
int string call_notification_hang_up_action 0x7f090006
int string call_notification_incoming_text 0x7f090007
int string call_notification_ongoing_text 0x7f090008
int string call_notification_screening_text 0x7f090009
int string close_drawer 0x7f09000a
int string close_sheet 0x7f09000b
int string default_error_message 0x7f09000c
int string default_popup_window_title 0x7f09000d
int string dropdown_menu 0x7f09000e
int string in_progress 0x7f09000f
int string indeterminate 0x7f090010
int string m3c_bottom_sheet_collapse_description 0x7f090011
int string m3c_bottom_sheet_dismiss_description 0x7f090012
int string m3c_bottom_sheet_drag_handle_description 0x7f090013
int string m3c_bottom_sheet_expand_description 0x7f090014
int string m3c_bottom_sheet_pane_title 0x7f090015
int string m3c_date_input_headline 0x7f090016
int string m3c_date_input_headline_description 0x7f090017
int string m3c_date_input_invalid_for_pattern 0x7f090018
int string m3c_date_input_invalid_not_allowed 0x7f090019
int string m3c_date_input_invalid_year_range 0x7f09001a
int string m3c_date_input_label 0x7f09001b
int string m3c_date_input_no_input_description 0x7f09001c
int string m3c_date_input_title 0x7f09001d
int string m3c_date_picker_headline 0x7f09001e
int string m3c_date_picker_headline_description 0x7f09001f
int string m3c_date_picker_navigate_to_year_description 0x7f090020
int string m3c_date_picker_no_selection_description 0x7f090021
int string m3c_date_picker_scroll_to_earlier_years 0x7f090022
int string m3c_date_picker_scroll_to_later_years 0x7f090023
int string m3c_date_picker_switch_to_calendar_mode 0x7f090024
int string m3c_date_picker_switch_to_day_selection 0x7f090025
int string m3c_date_picker_switch_to_input_mode 0x7f090026
int string m3c_date_picker_switch_to_next_month 0x7f090027
int string m3c_date_picker_switch_to_previous_month 0x7f090028
int string m3c_date_picker_switch_to_year_selection 0x7f090029
int string m3c_date_picker_title 0x7f09002a
int string m3c_date_picker_today_description 0x7f09002b
int string m3c_date_picker_year_picker_pane_title 0x7f09002c
int string m3c_date_range_input_invalid_range_input 0x7f09002d
int string m3c_date_range_input_title 0x7f09002e
int string m3c_date_range_picker_day_in_range 0x7f09002f
int string m3c_date_range_picker_end_headline 0x7f090030
int string m3c_date_range_picker_scroll_to_next_month 0x7f090031
int string m3c_date_range_picker_scroll_to_previous_month 0x7f090032
int string m3c_date_range_picker_start_headline 0x7f090033
int string m3c_date_range_picker_title 0x7f090034
int string m3c_dialog 0x7f090035
int string m3c_dropdown_menu_collapsed 0x7f090036
int string m3c_dropdown_menu_expanded 0x7f090037
int string m3c_dropdown_menu_toggle 0x7f090038
int string m3c_search_bar_search 0x7f090039
int string m3c_snackbar_dismiss 0x7f09003a
int string m3c_suggestions_available 0x7f09003b
int string m3c_time_picker_am 0x7f09003c
int string m3c_time_picker_hour 0x7f09003d
int string m3c_time_picker_hour_24h_suffix 0x7f09003e
int string m3c_time_picker_hour_selection 0x7f09003f
int string m3c_time_picker_hour_suffix 0x7f090040
int string m3c_time_picker_hour_text_field 0x7f090041
int string m3c_time_picker_minute 0x7f090042
int string m3c_time_picker_minute_selection 0x7f090043
int string m3c_time_picker_minute_suffix 0x7f090044
int string m3c_time_picker_minute_text_field 0x7f090045
int string m3c_time_picker_period_toggle_description 0x7f090046
int string m3c_time_picker_pm 0x7f090047
int string m3c_tooltip_long_press_label 0x7f090048
int string m3c_tooltip_pane_description 0x7f090049
int string navigation_menu 0x7f09004a
int string not_selected 0x7f09004b
int string range_end 0x7f09004c
int string range_start 0x7f09004d
int string selected 0x7f09004e
int string state_empty 0x7f09004f
int string state_off 0x7f090050
int string state_on 0x7f090051
int string status_bar_notification_info_overflow 0x7f090052
int string stop_recording 0x7f090053
int string switch_role 0x7f090054
int string tab 0x7f090055
int string template_percent 0x7f090056
int string tooltip_description 0x7f090057
int string tooltip_label 0x7f090058
int style DialogWindowTheme 0x7f0a0000
int style EdgeToEdgeFloatingDialogTheme 0x7f0a0001
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0a0002
int style FloatingDialogTheme 0x7f0a0003
int style FloatingDialogWindowTheme 0x7f0a0004
int style TextAppearance_Compat_Notification 0x7f0a0005
int style TextAppearance_Compat_Notification_Info 0x7f0a0006
int style TextAppearance_Compat_Notification_Line2 0x7f0a0007
int style TextAppearance_Compat_Notification_Time 0x7f0a0008
int style TextAppearance_Compat_Notification_Title 0x7f0a0009
int style Theme_VoiceMessageApp 0x7f0a000a
int style Widget_Compat_NotificationActionContainer 0x7f0a000b
int style Widget_Compat_NotificationActionText 0x7f0a000c
int[] styleable ActivityNavigator { 0x01010003, 0x7f010000, 0x7f010003, 0x7f010004, 0x7f010024 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable Capability { 0x7f01001f, 0x7f010022 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f010001, 0x7f010014 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f010009, 0x7f01000a, 0x7f01000b, 0x7f01000c, 0x7f01000d, 0x7f01000e, 0x7f01000f }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f010008, 0x7f010010, 0x7f010011, 0x7f010012, 0x7f010025 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable NavAction { 0x010100d0, 0x7f010005, 0x7f010006, 0x7f010007, 0x7f010015, 0x7f01001a, 0x7f01001b, 0x7f01001c, 0x7f01001d, 0x7f01001e, 0x7f010020 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f010002, 0x7f010019 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f010000, 0x7f010016, 0x7f010026 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f010023 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f010017 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f010013 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f010021 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int xml backup_rules 0x7f0c0000
int xml data_extraction_rules 0x7f0c0001
