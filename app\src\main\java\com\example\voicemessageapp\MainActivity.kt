package com.example.voicemessageapp

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.core.content.ContextCompat
import com.example.voicemessageapp.ui.navigation.AppNavigation // Import AppNavigation
import com.example.voicemessageapp.ui.theme.VoiceMessageAppTheme

class MainActivity : ComponentActivity() {

    // MutableState to hold the permission status, can be observed by Composables if needed,
    // or used to trigger actions after permission is granted/denied.
    var hasRecordAudioPermission by mutableStateOf(false)
        private set

    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted: Boolean ->
            hasRecordAudioPermission = isGranted
            if (isGranted) {
                // Permission is granted. Continue the action or workflow in your app.
                // You might want to notify a ViewModel or a Composable.
                // For now, just log.
                android.util.Log.d("MainActivity", "RECORD_AUDIO permission granted.")
            } else {
                // Explain to the user that the feature is unavailable because the
                // feature requires a permission that the user has denied. At the
                // same time, respect the user's decision. Don't link to system
                // settings in an effort to convince the user to change their
                // decision.
                android.util.Log.d("MainActivity", "RECORD_AUDIO permission denied.")
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        updatePermissionStatus() // Initial check
        enableEdgeToEdge()
        setContent {
            VoiceMessageAppTheme {
                // Pass a lambda to request permission to AppNavigation/MainScreen
                AppNavigation(
                    requestAudioPermission = ::requestAudioPermission,
                    hasRecordAudioPermission = hasRecordAudioPermission
                )
            }
        }
    }

    private fun updatePermissionStatus() {
        hasRecordAudioPermission = ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    // This function can be called from Composables (via ViewModel or directly if structured so)
    // to request the permission.
    fun requestAudioPermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED -> {
                // Permission is already granted
                hasRecordAudioPermission = true
                android.util.Log.d("MainActivity", "RECORD_AUDIO permission already granted.")
            }
            shouldShowRequestPermissionRationale(Manifest.permission.RECORD_AUDIO) -> {
                // TODO: Show an educational UI to the user explaining why the permission is needed.
                // For now, just request directly.
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
            else -> {
                // Directly request the permission.
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }
}

// Removed Greeting and GreetingPreview as they are no longer used directly here.
// MainScreen.kt now has its own preview.
