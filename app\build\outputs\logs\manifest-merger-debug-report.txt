-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:2:1-35:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:2:1-35:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:2:1-35:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c20c12a44472434f4dc1af8307e33480\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\803cf40d8254fc8ab524e94b8e875741\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d06dee99f4d9815fc5fef1d144b3a38\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\542e09cc1cbed656b72c97bc05800fe8\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\facec4bdda5f139df5ef606774db9130\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5db06cec49475e44cc2423ccf1ecc1d1\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdb79753f4696a11bd498810730fd1e\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\208357e46cdeb20eb9d04cdfa69cd8d8\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee7d57914d6cf982e5bf93d1be98a36a\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b8e09b1e2d9bcaf24ab6c151bc881d5\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9b7e027199169ce29daad9dfa8cfb60\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd9d6162cd50d8ff84acaffca5e2fb2\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6a0553964c02a0b783da88650dd2aa\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975f0cd22810d1ad5f91fa1c0a2dc7f3\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8818f73d59e2e690e7fda9d8cb0efa\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29ebf643b17223b883a7b0f6cc04a48c\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69857a384fb1a1119b18a502c0bb1a86\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3052531180382b5796332eaec50e9f2e\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9d6ae19dd1f4cf892adad44b3d51a76\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27018166a8ac18b4f22775b2778e31a\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\833f49b716504e2fa93244c6fdd0befb\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8640cb4947671b720a6d0d1fe63b233e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c10cdef5bf51a79439f45c973b430bd\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8450235fe291a087c01036532ebab36\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7684fdbc65ec9378bcf408e5a7aa812b\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f787ca0fcf232f429c63f50bfec6dee\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4e04b534ff881fdba2263af5fd9c1a2\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e0e413fe716114ea1c9ea106e8d9a7\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e9be5bd89b801bba13f97beb17965d7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d089c7653ba8311316b8282329723f\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad37a182f5dcf887610721b3c1dce927\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df53ad0e512d118ebe2341d797e63211\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840b1a464e68ce12ab077f963ec924b0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f965ff1e55f0eaa4c7987c0da2824a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d83e120b4aecc09e40809f52f78eaf0d\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c18b7436b0df895071cf8dd718aac4\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff51c5a90343cc99ece3adf1ef9d865\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5860b1ecc550a7dc8fe0cae827ea0070\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454f624047b51193823cf4cdd4c03eaf\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d122e06e3bdf3c58781e850453f00f35\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccbefc449fed16eb939c7e9675351824\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7190916b469b7f053b5bdb9b73a16ee2\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6458eb53202437f263578ccda60f15c2\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd10e3940227e5ba1721160217e3bda\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73180d248ea95f049ddc15d18077129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7b7265e9a3a39f8911e47a190f113d8\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\414fbf4f7fdabdc47a3c64a4adb635bb\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e238c4a211b022805ce697d523ca3063\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:7:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:7:22-69
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:11:5-33:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:11:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73180d248ea95f049ddc15d18077129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73180d248ea95f049ddc15d18077129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:17:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:15:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:18:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:21:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:16:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:20:9-53
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:14:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:12:9-40
activity#com.example.voicemessageapp.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:22:9-32:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:25:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:24:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:26:13-57
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:23:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:27:13-31:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:30:27-74
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c20c12a44472434f4dc1af8307e33480\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c20c12a44472434f4dc1af8307e33480\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\803cf40d8254fc8ab524e94b8e875741\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\803cf40d8254fc8ab524e94b8e875741\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d06dee99f4d9815fc5fef1d144b3a38\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d06dee99f4d9815fc5fef1d144b3a38\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\542e09cc1cbed656b72c97bc05800fe8\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\542e09cc1cbed656b72c97bc05800fe8\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\facec4bdda5f139df5ef606774db9130\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\facec4bdda5f139df5ef606774db9130\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5db06cec49475e44cc2423ccf1ecc1d1\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5db06cec49475e44cc2423ccf1ecc1d1\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdb79753f4696a11bd498810730fd1e\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdb79753f4696a11bd498810730fd1e\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\208357e46cdeb20eb9d04cdfa69cd8d8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\208357e46cdeb20eb9d04cdfa69cd8d8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee7d57914d6cf982e5bf93d1be98a36a\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee7d57914d6cf982e5bf93d1be98a36a\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b8e09b1e2d9bcaf24ab6c151bc881d5\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b8e09b1e2d9bcaf24ab6c151bc881d5\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9b7e027199169ce29daad9dfa8cfb60\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9b7e027199169ce29daad9dfa8cfb60\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd9d6162cd50d8ff84acaffca5e2fb2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd9d6162cd50d8ff84acaffca5e2fb2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6a0553964c02a0b783da88650dd2aa\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6a0553964c02a0b783da88650dd2aa\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975f0cd22810d1ad5f91fa1c0a2dc7f3\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\975f0cd22810d1ad5f91fa1c0a2dc7f3\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8818f73d59e2e690e7fda9d8cb0efa\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8818f73d59e2e690e7fda9d8cb0efa\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29ebf643b17223b883a7b0f6cc04a48c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29ebf643b17223b883a7b0f6cc04a48c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69857a384fb1a1119b18a502c0bb1a86\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69857a384fb1a1119b18a502c0bb1a86\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3052531180382b5796332eaec50e9f2e\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3052531180382b5796332eaec50e9f2e\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9d6ae19dd1f4cf892adad44b3d51a76\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9d6ae19dd1f4cf892adad44b3d51a76\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27018166a8ac18b4f22775b2778e31a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27018166a8ac18b4f22775b2778e31a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\833f49b716504e2fa93244c6fdd0befb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\833f49b716504e2fa93244c6fdd0befb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8640cb4947671b720a6d0d1fe63b233e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8640cb4947671b720a6d0d1fe63b233e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c10cdef5bf51a79439f45c973b430bd\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c10cdef5bf51a79439f45c973b430bd\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8450235fe291a087c01036532ebab36\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8450235fe291a087c01036532ebab36\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7684fdbc65ec9378bcf408e5a7aa812b\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7684fdbc65ec9378bcf408e5a7aa812b\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f787ca0fcf232f429c63f50bfec6dee\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f787ca0fcf232f429c63f50bfec6dee\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4e04b534ff881fdba2263af5fd9c1a2\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4e04b534ff881fdba2263af5fd9c1a2\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e0e413fe716114ea1c9ea106e8d9a7\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e0e413fe716114ea1c9ea106e8d9a7\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e9be5bd89b801bba13f97beb17965d7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e9be5bd89b801bba13f97beb17965d7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d089c7653ba8311316b8282329723f\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d089c7653ba8311316b8282329723f\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad37a182f5dcf887610721b3c1dce927\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad37a182f5dcf887610721b3c1dce927\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df53ad0e512d118ebe2341d797e63211\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df53ad0e512d118ebe2341d797e63211\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840b1a464e68ce12ab077f963ec924b0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840b1a464e68ce12ab077f963ec924b0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f965ff1e55f0eaa4c7987c0da2824a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6f965ff1e55f0eaa4c7987c0da2824a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d83e120b4aecc09e40809f52f78eaf0d\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d83e120b4aecc09e40809f52f78eaf0d\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c18b7436b0df895071cf8dd718aac4\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c18b7436b0df895071cf8dd718aac4\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff51c5a90343cc99ece3adf1ef9d865\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff51c5a90343cc99ece3adf1ef9d865\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5860b1ecc550a7dc8fe0cae827ea0070\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5860b1ecc550a7dc8fe0cae827ea0070\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454f624047b51193823cf4cdd4c03eaf\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\454f624047b51193823cf4cdd4c03eaf\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d122e06e3bdf3c58781e850453f00f35\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d122e06e3bdf3c58781e850453f00f35\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccbefc449fed16eb939c7e9675351824\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccbefc449fed16eb939c7e9675351824\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7190916b469b7f053b5bdb9b73a16ee2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7190916b469b7f053b5bdb9b73a16ee2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6458eb53202437f263578ccda60f15c2\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6458eb53202437f263578ccda60f15c2\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd10e3940227e5ba1721160217e3bda\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd10e3940227e5ba1721160217e3bda\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73180d248ea95f049ddc15d18077129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73180d248ea95f049ddc15d18077129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7b7265e9a3a39f8911e47a190f113d8\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7b7265e9a3a39f8911e47a190f113d8\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\414fbf4f7fdabdc47a3c64a4adb635bb\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\414fbf4f7fdabdc47a3c64a4adb635bb\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e238c4a211b022805ce697d523ca3063\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e238c4a211b022805ce697d523ca3063\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d96c9dcad915359a61fb1ee5badbbfc\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4475b11b4dbe2d8e0904e6f7eec7125f\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4c7397c7afd031850287bfadff5a424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.voicemessageapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.voicemessageapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
