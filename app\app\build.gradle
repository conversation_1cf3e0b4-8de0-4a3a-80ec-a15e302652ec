plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.example.voicemessageapp'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.voicemessageapp"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    def compose_version = "1.4.3"
    
    implementation platform('androidx.compose:compose-bom:2023.08.00')
    androidTestImplementation platform('androidx.compose:compose-bom:2023.08.00')

    // Material Design 3
    implementation 'androidx.compose.material3:material3:1.1.2'
    
    // Material Icons
    implementation "androidx.compose.material:material:$compose_version"
    implementation "androidx.compose.material:material-icons-core:$compose_version"
    implementation "androidx.compose.material:material-icons-extended:$compose_version"
    
    // Android Studio Preview support
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"

    // UI Tests
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:$compose_version"

    // Optional - Integration with activities
    implementation 'androidx.activity:activity-compose:1.7.2'
    
    // Optional - Integration with ViewModels
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2'
    
    // Optional - Integration with LiveData
    implementation "androidx.compose.runtime:runtime-livedata:$compose_version"
    
    // Optional - Integration with RxJava
    implementation "androidx.compose.runtime:runtime-rxjava2:$compose_version"

    // Room components
    implementation "androidx.room:room-runtime:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"

    // Navigation
    implementation "androidx.navigation:navigation-compose:2.7.5"

    // Accompanist
    implementation "com.google.accompanist:accompanist-permissions:0.31.3-beta"

    // Core Android dependencies
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
} 