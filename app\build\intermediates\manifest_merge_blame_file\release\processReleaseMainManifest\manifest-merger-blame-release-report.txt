1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.voicemessageapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_CONTACTS" />
13-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:7:22-69
14
15    <permission
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.voicemessageapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.voicemessageapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Optional: if you plan to save audio files to external storage -->
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
20    <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" /> -->
21    <application
21-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:11:5-33:19
22        android:name="com.example.voicemessageapp.VoiceMessageApp"
22-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:12:9-40
23        android:allowBackup="true"
23-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:13:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0bdf927d9c583a8851b6fb66b6b215\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:14:9-65
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:15:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:16:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:17:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:18:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:19:9-35
32        android:theme="@style/Theme.VoiceMessageApp" >
32-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:20:9-53
33        <activity
33-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:22:9-32:20
34            android:name="com.example.voicemessageapp.MainActivity"
34-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:23:13-41
35            android:exported="true"
35-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:24:13-36
36            android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:25:13-45
37            android:theme="@style/Theme.VoiceMessageApp" >
37-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:26:13-57
38            <intent-filter>
38-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:27:13-31:29
39                <action android:name="android.intent.action.MAIN" />
39-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:28:17-69
39-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:28:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:30:17-77
41-->C:\Users\<USER>\AndroidStudioProjects\VoiceMessageApp\app\src\main\AndroidManifest.xml:30:27-74
42            </intent-filter>
43        </activity>
44
45        <service
45-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
46            android:name="androidx.room.MultiInstanceInvalidationService"
46-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
47            android:directBootAware="true"
47-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
48            android:exported="false" />
48-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6964e4bbe03063d5ba31aa3c3459b732\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
49
50        <provider
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
51            android:name="androidx.startup.InitializationProvider"
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
52            android:authorities="com.example.voicemessageapp.androidx-startup"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
53            android:exported="false" >
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
54            <meta-data
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.emoji2.text.EmojiCompatInitializer"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
56                android:value="androidx.startup" />
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457a21172164276754a24346d3120ff9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
58-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
59                android:value="androidx.startup" />
59-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4915bc79310dadcda5bdc6f990011ad4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
62                android:value="androidx.startup" />
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
63        </provider>
64
65        <receiver
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
66            android:name="androidx.profileinstaller.ProfileInstallReceiver"
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
67            android:directBootAware="false"
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
68            android:enabled="true"
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
69            android:exported="true"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
70            android:permission="android.permission.DUMP" >
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
72                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
75                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
78                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
81                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c549031677c781def64c4313713a1d7\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
82            </intent-filter>
83        </receiver>
84    </application>
85
86</manifest>
