"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ed31c739d655489568c7319390ecc3f6\\transformed\\navigation-common-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ea2ebda96d8a77aaf0bf8c0d5c4e33f7\\transformed\\navigation-runtime-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\405ed62455e6be458dcaea7337174ed2\\transformed\\navigation-common-ktx-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8a0f3ca2b31307d4538a8ac358c43e8f\\transformed\\navigation-runtime-ktx-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\426770074d3728ca0b370852c9c713d0\\transformed\\navigation-compose-2.7.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\96fb7c6c88c63bcd26703a3dcf0b116e\\transformed\\material3-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7631e4af68abf84bc6008d701566271f\\transformed\\foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7108e1f6dd633032d0af204f2b2d8be4\\transformed\\material-ripple-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\06b075a1542da03a4426e35159a53c76\\transformed\\foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2ee60cdaf66ab3153d900f69d261819e\\transformed\\animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\52232ec843f51a1545c1ad354568e154\\transformed\\animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7882f1f7ec70ead64e52e460bea17ed6\\transformed\\ui-util-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a554812f3d934ab31f321243c527fef0\\transformed\\ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\395b21f5e3d0de2803c97d34abec136a\\transformed\\ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1d434ba14f25255c2a381af7501827b\\transformed\\ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a995530bb5481b6e9ae03ed87c46532\\transformed\\ui-tooling-data-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\efdea5d6492ee0de51033ee47e6e8a47\\transformed\\ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\668cf1189c2e65299a9f44de96d279ae\\transformed\\ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8d70e17d78e71743f0098f34d2183d5c\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-jvm\\2.8.3\\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\\lifecycle-common-jvm-2.8.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a4e4cbc69fa44d70a39d652d7569ceb5\\transformed\\lifecycle-runtime-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\92306b94806eb3079dc88a8edcc46607\\transformed\\lifecycle-livedata-core-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b103371fb553b069bc2b5c5b44a994b7\\transformed\\lifecycle-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7ea2531961636de766cd59e6a3704807\\transformed\\lifecycle-viewmodel-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\329889d0966925932eafd7d910ac8d25\\transformed\\lifecycle-viewmodel-ktx-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\33a27900ce94f00e05f941419fb0fb9e\\transformed\\lifecycle-viewmodel-savedstate-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f0505458cfca87a13959fb85d49ff77b\\transformed\\lifecycle-runtime-ktx-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\874b43df8de932d7be81b4b98edf7eb8\\transformed\\lifecycle-viewmodel-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d040bb772fdc4580eccc030aff96324f\\transformed\\material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8e8ce1df4e30d1ca5027b16e9fe46e8a\\transformed\\ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cc16e0568618a8bfb14853e72c9ef4b8\\transformed\\ui-tooling-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a66640b8ce26b94fe0af63115329be82\\transformed\\ui-test-manifest-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bf34cba2a84a9dcb411ed438d98e96d1\\transformed\\activity-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a13f468a2d9c690a4e36017e843efd1e\\transformed\\activity-compose-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e169342bdce337af0729bfc9323aac85\\transformed\\activity-ktx-1.8.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d02122ec0f54102b21603015fed940a3\\transformed\\core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.room\\room-common\\2.6.1\\ff1b9580850a9b7eef56554e356628d225785265\\room-common-2.6.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\84b4ad7a18be1d82ced3d88f8174e620\\transformed\\room-runtime-2.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d77c84b20d8df019ce22c85e866404c6\\transformed\\room-ktx-2.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\logging-interceptor\\4.12.0\\e922c1f14d365c0f2bed140cc0825e18462c2778\\logging-interceptor-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\okhttp\\4.12.0\\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\\okhttp-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9980b6b87e96633691939eb06880c793\\transformed\\runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a5bcb3f44e2988340c876a1d4fe4d675\\transformed\\runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.3\\2b09627576f0989a436a00a4a54b55fa5026fb86\\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.3\\38d9cad3a0b03a10453b56577984bdeb48edeed5\\kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okio\\okio-jvm\\3.6.0\\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\\okio-jvm-3.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.9.10\\c7510d64a83411a649c76f2778304ddf71d7437b\\kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\32d9aa05161ffa374699d9977dad1bbc\\transformed\\savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\94b1823ce63e7aa4e496e551b2b0280b\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b86775da1ef5c0d092602e142874a71f\\transformed\\sqlite-framework-2.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f7c7a6bf17745a6fb6326c1b32a7b2f\\transformed\\sqlite-2.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a911491bc76e6e23259305f9d08f77db\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-jvm\\1.4.0\\e209fb7bd1183032f55a0408121c6251a81acb49\\collection-jvm-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.8.0\\b8a16fe526014b7941c1debaccaf9c5153692dbb\\annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\53abcb7a967da2f999612f494938228e\\transformed\\annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.9.10\\bc5bfc2690338defd5195b05c57562f2194eeb10\\kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.0.21\\618b539767b4899b4660a83006e052b63f1db551\\kotlin-stdlib-2.0.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\build\\tmp\\kapt3\\incrementalData\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-compose-compiler-plugin-embeddable\\2.0.21\\e14f003d962fb25693b461de59490c91072a7979\\kotlin-compose-compiler-plugin-embeddable-2.0.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-annotation-processing-gradle\\2.0.21\\b3be9823176d79cb0fc710e77309cfe599be9abf\\kotlin-annotation-processing-gradle-2.0.21.jar" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:generateFunctionKeyMetaClasses=false,plugin:androidx.compose.compiler.plugins.kotlin:traceMarkersEnabled=true,plugin:org.jetbrains.kotlin.kapt3:configuration=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" "-Xuse-inline-scopes-numbers" "-Xallow-unstable-dependencies" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\data\\AppDatabase.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\data\\Message.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\data\\MessageDao.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\MainActivity.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\service\\VoiceToTextService.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\ui\\navigation\\AppNavigation.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\ui\\screens\\MainScreen.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\ui\\theme\\Color.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\ui\\theme\\Theme.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\ui\\theme\\Type.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\ui\\viewmodel\\MainViewModel.kt" "C:\\Users\\<USER>\\AndroidStudioProjects\\VoiceMessageApp\\app\\src\\main\\java\\com\\example\\voicemessageapp\\VoiceMessageApp.kt"