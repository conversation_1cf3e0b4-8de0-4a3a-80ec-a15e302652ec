  Manifest android  Activity android.app  Application android.app  ActivityResultContracts android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  android android.app.Activity  getValue android.app.Activity  mutableStateOf android.app.Activity  provideDelegate android.app.Activity  registerForActivityResult android.app.Activity  setValue android.app.Activity  Context android.content  ActivityResultContracts android.content.Context  Boolean android.content.Context  Bundle android.content.Context  android android.content.Context  getValue android.content.Context  mutableStateOf android.content.Context  provideDelegate android.content.Context  registerForActivityResult android.content.Context  setValue android.content.Context  ActivityResultContracts android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  android android.content.ContextWrapper  getValue android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  setValue android.content.ContextWrapper  PackageManager android.content.pm  Build 
android.os  Bundle 
android.os  Log android.util  d android.util.Log  ActivityResultContracts  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  Icons androidx.compose.material.icons  Add &androidx.compose.material.icons.filled  Mic &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
MaterialTheme androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  MutableState androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  getGETValue %androidx.compose.runtime.MutableState  getGetValue %androidx.compose.runtime.MutableState  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  getValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  AndroidViewModel androidx.lifecycle  	Lifecycle androidx.lifecycle  LifecycleEventObserver androidx.lifecycle  viewModelScope androidx.lifecycle  AppDatabase #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  Flow #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  Long #androidx.lifecycle.AndroidViewModel  Message #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  AppDatabase androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  Message androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  
NavController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AppDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  
MessageDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
messageDao androidx.room.RoomDatabase  google com  ActivityResultContracts com.example.voicemessageapp  Boolean com.example.voicemessageapp  MainActivity com.example.voicemessageapp  R com.example.voicemessageapp  VoiceMessageApp com.example.voicemessageapp  android com.example.voicemessageapp  getValue com.example.voicemessageapp  mutableStateOf com.example.voicemessageapp  provideDelegate com.example.voicemessageapp  setValue com.example.voicemessageapp  ActivityResultContracts (com.example.voicemessageapp.MainActivity  Boolean (com.example.voicemessageapp.MainActivity  Bundle (com.example.voicemessageapp.MainActivity  android (com.example.voicemessageapp.MainActivity  
getANDROID (com.example.voicemessageapp.MainActivity  
getAndroid (com.example.voicemessageapp.MainActivity  getGETValue (com.example.voicemessageapp.MainActivity  getGetValue (com.example.voicemessageapp.MainActivity  getMUTABLEStateOf (com.example.voicemessageapp.MainActivity  getMutableStateOf (com.example.voicemessageapp.MainActivity  getPROVIDEDelegate (com.example.voicemessageapp.MainActivity  getProvideDelegate (com.example.voicemessageapp.MainActivity  getSETValue (com.example.voicemessageapp.MainActivity  getSetValue (com.example.voicemessageapp.MainActivity  getValue (com.example.voicemessageapp.MainActivity  hasRecordAudioPermission (com.example.voicemessageapp.MainActivity  mutableStateOf (com.example.voicemessageapp.MainActivity  provideDelegate (com.example.voicemessageapp.MainActivity  registerForActivityResult (com.example.voicemessageapp.MainActivity  setValue (com.example.voicemessageapp.MainActivity  AppDatabase  com.example.voicemessageapp.data  Boolean  com.example.voicemessageapp.data  List  com.example.voicemessageapp.data  Long  com.example.voicemessageapp.data  Message  com.example.voicemessageapp.data  
MessageDao  com.example.voicemessageapp.data  OnConflictStrategy  com.example.voicemessageapp.data  String  com.example.voicemessageapp.data  Volatile  com.example.voicemessageapp.data  AppDatabase ,com.example.voicemessageapp.data.AppDatabase  Context ,com.example.voicemessageapp.data.AppDatabase  
MessageDao ,com.example.voicemessageapp.data.AppDatabase  Volatile ,com.example.voicemessageapp.data.AppDatabase  getDatabase ,com.example.voicemessageapp.data.AppDatabase  
messageDao ,com.example.voicemessageapp.data.AppDatabase  AppDatabase 6com.example.voicemessageapp.data.AppDatabase.Companion  Context 6com.example.voicemessageapp.data.AppDatabase.Companion  
MessageDao 6com.example.voicemessageapp.data.AppDatabase.Companion  Volatile 6com.example.voicemessageapp.data.AppDatabase.Companion  getDatabase 6com.example.voicemessageapp.data.AppDatabase.Companion  Boolean (com.example.voicemessageapp.data.Message  Long (com.example.voicemessageapp.data.Message  
PrimaryKey (com.example.voicemessageapp.data.Message  String (com.example.voicemessageapp.data.Message  Delete +com.example.voicemessageapp.data.MessageDao  Flow +com.example.voicemessageapp.data.MessageDao  Insert +com.example.voicemessageapp.data.MessageDao  List +com.example.voicemessageapp.data.MessageDao  Long +com.example.voicemessageapp.data.MessageDao  Message +com.example.voicemessageapp.data.MessageDao  OnConflictStrategy +com.example.voicemessageapp.data.MessageDao  Query +com.example.voicemessageapp.data.MessageDao  String +com.example.voicemessageapp.data.MessageDao  Update +com.example.voicemessageapp.data.MessageDao  getAllMessages +com.example.voicemessageapp.data.MessageDao  MutableStateFlow #com.example.voicemessageapp.service  OkHttpClient #com.example.voicemessageapp.service  SocketStatus #com.example.voicemessageapp.service  String #com.example.voicemessageapp.service  TimeUnit #com.example.voicemessageapp.service  VoiceToTextService #com.example.voicemessageapp.service  DISCONNECTED 0com.example.voicemessageapp.service.SocketStatus  SocketStatus 0com.example.voicemessageapp.service.SocketStatus  String 0com.example.voicemessageapp.service.SocketStatus  String 6com.example.voicemessageapp.service.SocketStatus.ERROR  
ByteString 6com.example.voicemessageapp.service.VoiceToTextService  MutableStateFlow 6com.example.voicemessageapp.service.VoiceToTextService  OkHttpClient 6com.example.voicemessageapp.service.VoiceToTextService  SocketStatus 6com.example.voicemessageapp.service.VoiceToTextService  	StateFlow 6com.example.voicemessageapp.service.VoiceToTextService  String 6com.example.voicemessageapp.service.VoiceToTextService  TimeUnit 6com.example.voicemessageapp.service.VoiceToTextService  	WebSocket 6com.example.voicemessageapp.service.VoiceToTextService  _connectionStatus 6com.example.voicemessageapp.service.VoiceToTextService  _textResult 6com.example.voicemessageapp.service.VoiceToTextService  AppDestinations )com.example.voicemessageapp.ui.navigation  
AppNavigation )com.example.voicemessageapp.ui.navigation  Boolean )com.example.voicemessageapp.ui.navigation  Unit )com.example.voicemessageapp.ui.navigation  ExperimentalMaterial3Api &com.example.voicemessageapp.ui.screens  ExperimentalPermissionsApi &com.example.voicemessageapp.ui.screens  
MainScreen &com.example.voicemessageapp.ui.screens  MainScreenPreview &com.example.voicemessageapp.ui.screens  OptIn &com.example.voicemessageapp.ui.screens  Boolean $com.example.voicemessageapp.ui.theme  DarkColorScheme $com.example.voicemessageapp.ui.theme  LightColorScheme $com.example.voicemessageapp.ui.theme  Pink40 $com.example.voicemessageapp.ui.theme  Pink80 $com.example.voicemessageapp.ui.theme  Purple40 $com.example.voicemessageapp.ui.theme  Purple80 $com.example.voicemessageapp.ui.theme  PurpleGrey40 $com.example.voicemessageapp.ui.theme  PurpleGrey80 $com.example.voicemessageapp.ui.theme  
Typography $com.example.voicemessageapp.ui.theme  Unit $com.example.voicemessageapp.ui.theme  VoiceMessageAppTheme $com.example.voicemessageapp.ui.theme  AppDatabase (com.example.voicemessageapp.ui.viewmodel  Boolean (com.example.voicemessageapp.ui.viewmodel  List (com.example.voicemessageapp.ui.viewmodel  Long (com.example.voicemessageapp.ui.viewmodel  
MainViewModel (com.example.voicemessageapp.ui.viewmodel  MutableStateFlow (com.example.voicemessageapp.ui.viewmodel  String (com.example.voicemessageapp.ui.viewmodel  	emptyList (com.example.voicemessageapp.ui.viewmodel  AppDatabase 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  Application 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  Boolean 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  Flow 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  List 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  Long 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  Message 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  MutableStateFlow 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  	StateFlow 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  String 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  _hasAudioPermission 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  _isRecording 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  _searchResults 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  _searchText 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  _selectedMessage 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  	emptyList 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  getEMPTYList 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  getEmptyList 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  
messageDao 6com.example.voicemessageapp.ui.viewmodel.MainViewModel  ActivityResultContracts 	java.lang  AppDatabase 	java.lang  ExperimentalMaterial3Api 	java.lang  ExperimentalPermissionsApi 	java.lang  Message 	java.lang  MutableStateFlow 	java.lang  OkHttpClient 	java.lang  OnConflictStrategy 	java.lang  SocketStatus 	java.lang  TimeUnit 	java.lang  android 	java.lang  	emptyList 	java.lang  getValue 	java.lang  mutableStateOf 	java.lang  provideDelegate 	java.lang  setValue 	java.lang  TimeUnit java.util.concurrent  MILLISECONDS java.util.concurrent.TimeUnit  ActivityResultContracts kotlin  AppDatabase kotlin  Array kotlin  Boolean kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  ExperimentalPermissionsApi kotlin  	Function1 kotlin  Int kotlin  Long kotlin  Message kotlin  MutableStateFlow kotlin  Nothing kotlin  OkHttpClient kotlin  OnConflictStrategy kotlin  OptIn kotlin  SocketStatus kotlin  String kotlin  TimeUnit kotlin  Unit kotlin  Volatile kotlin  android kotlin  arrayOf kotlin  	emptyList kotlin  getValue kotlin  mutableStateOf kotlin  provideDelegate kotlin  setValue kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  ActivityResultContracts kotlin.annotation  AppDatabase kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  ExperimentalPermissionsApi kotlin.annotation  Message kotlin.annotation  MutableStateFlow kotlin.annotation  OkHttpClient kotlin.annotation  OnConflictStrategy kotlin.annotation  SocketStatus kotlin.annotation  TimeUnit kotlin.annotation  Volatile kotlin.annotation  android kotlin.annotation  	emptyList kotlin.annotation  getValue kotlin.annotation  mutableStateOf kotlin.annotation  provideDelegate kotlin.annotation  setValue kotlin.annotation  ActivityResultContracts kotlin.collections  AppDatabase kotlin.collections  ExperimentalMaterial3Api kotlin.collections  ExperimentalPermissionsApi kotlin.collections  List kotlin.collections  Message kotlin.collections  MutableStateFlow kotlin.collections  OkHttpClient kotlin.collections  OnConflictStrategy kotlin.collections  SocketStatus kotlin.collections  TimeUnit kotlin.collections  Volatile kotlin.collections  android kotlin.collections  	emptyList kotlin.collections  getValue kotlin.collections  mutableStateOf kotlin.collections  provideDelegate kotlin.collections  setValue kotlin.collections  ActivityResultContracts kotlin.comparisons  AppDatabase kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  ExperimentalPermissionsApi kotlin.comparisons  Message kotlin.comparisons  MutableStateFlow kotlin.comparisons  OkHttpClient kotlin.comparisons  OnConflictStrategy kotlin.comparisons  SocketStatus kotlin.comparisons  TimeUnit kotlin.comparisons  Volatile kotlin.comparisons  android kotlin.comparisons  	emptyList kotlin.comparisons  getValue kotlin.comparisons  mutableStateOf kotlin.comparisons  provideDelegate kotlin.comparisons  setValue kotlin.comparisons  ActivityResultContracts 	kotlin.io  AppDatabase 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  ExperimentalPermissionsApi 	kotlin.io  Message 	kotlin.io  MutableStateFlow 	kotlin.io  OkHttpClient 	kotlin.io  OnConflictStrategy 	kotlin.io  SocketStatus 	kotlin.io  TimeUnit 	kotlin.io  Volatile 	kotlin.io  android 	kotlin.io  	emptyList 	kotlin.io  getValue 	kotlin.io  mutableStateOf 	kotlin.io  provideDelegate 	kotlin.io  setValue 	kotlin.io  ActivityResultContracts 
kotlin.jvm  AppDatabase 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  ExperimentalPermissionsApi 
kotlin.jvm  Message 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OkHttpClient 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  SocketStatus 
kotlin.jvm  TimeUnit 
kotlin.jvm  Volatile 
kotlin.jvm  android 
kotlin.jvm  	emptyList 
kotlin.jvm  getValue 
kotlin.jvm  mutableStateOf 
kotlin.jvm  provideDelegate 
kotlin.jvm  setValue 
kotlin.jvm  ActivityResultContracts 
kotlin.ranges  AppDatabase 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  ExperimentalPermissionsApi 
kotlin.ranges  Message 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OkHttpClient 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  SocketStatus 
kotlin.ranges  TimeUnit 
kotlin.ranges  Volatile 
kotlin.ranges  android 
kotlin.ranges  	emptyList 
kotlin.ranges  getValue 
kotlin.ranges  mutableStateOf 
kotlin.ranges  provideDelegate 
kotlin.ranges  setValue 
kotlin.ranges  KClass kotlin.reflect  ActivityResultContracts kotlin.sequences  AppDatabase kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  ExperimentalPermissionsApi kotlin.sequences  Message kotlin.sequences  MutableStateFlow kotlin.sequences  OkHttpClient kotlin.sequences  OnConflictStrategy kotlin.sequences  SocketStatus kotlin.sequences  TimeUnit kotlin.sequences  Volatile kotlin.sequences  android kotlin.sequences  	emptyList kotlin.sequences  getValue kotlin.sequences  mutableStateOf kotlin.sequences  provideDelegate kotlin.sequences  setValue kotlin.sequences  ActivityResultContracts kotlin.text  AppDatabase kotlin.text  ExperimentalMaterial3Api kotlin.text  ExperimentalPermissionsApi kotlin.text  Message kotlin.text  MutableStateFlow kotlin.text  OkHttpClient kotlin.text  OnConflictStrategy kotlin.text  SocketStatus kotlin.text  TimeUnit kotlin.text  Volatile kotlin.text  android kotlin.text  	emptyList kotlin.text  getValue kotlin.text  mutableStateOf kotlin.text  provideDelegate kotlin.text  setValue kotlin.text  launch kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  	WebSocket okhttp3  WebSocketListener okhttp3  Builder okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  
ByteString okio  Boolean &com.example.voicemessageapp.ui.screens  Unit &com.example.voicemessageapp.ui.screens  Mic 'androidx.compose.material.icons.rounded                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      