package com.example.voicemessageapp.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.voicemessageapp.ui.screens.MainScreen

object AppDestinations {
    const val MAIN_SCREEN = "main"
    // Add other destinations here
    // const val SETTINGS_SCREEN = "settings"
    // const val CHAT_DETAIL_SCREEN = "chat_detail/{chatId}"
}

@Composable
fun AppNavigation(
    requestAudioPermission: () -> Unit,
    hasRecordAudioPermission: Boolean
) {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = AppDestinations.MAIN_SCREEN) {
        composable(AppDestinations.MAIN_SCREEN) {
            MainScreen(
                navController = navController,
                requestAudioPermission = requestAudioPermission,
                hasRecordAudioPermission = hasRecordAudioPermission
            )
        }
        // Add other composable routes here
        /*
        composable(AppDestinations.SETTINGS_SCREEN) {
            SettingsScreen(navController = navController)
        }
        composable(AppDestinations.CHAT_DETAIL_SCREEN) { backStackEntry ->
            val chatId = backStackEntry.arguments?.getString("chatId")
            ChatDetailScreen(navController = navController, chatId = chatId)
        }
        */
    }
}
