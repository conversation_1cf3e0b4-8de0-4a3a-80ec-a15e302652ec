package com.example.voicemessageapp.service

import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString
import java.util.concurrent.TimeUnit

class VoiceToTextService {

    private val client = OkHttpClient.Builder()
        .readTimeout(0, TimeUnit.MILLISECONDS) // Important for long-lived connections
        .build()

    private var webSocket: WebSocket? = null

    private val _textResult = MutableStateFlow<String?>(null)
    val textResult: StateFlow<String?> = _textResult

    private val _connectionStatus = MutableStateFlow<SocketStatus>(SocketStatus.DISCONNECTED)
    val connectionStatus: StateFlow<SocketStatus> = _connectionStatus

    private var webSocketUrl: String = "" // To be set before connecting

    fun setWebSocketUrl(url: String) {
        webSocketUrl = url
    }

    fun connect() {
        if (webSocketUrl.isEmpty()) {
            Log.e("VoiceToTextService", "WebSocket URL is not set.")
            _connectionStatus.value = SocketStatus.ERROR("URL not set")
            return
        }
        if (_connectionStatus.value == SocketStatus.CONNECTED || _connectionStatus.value == SocketStatus.CONNECTING) {
            Log.d("VoiceToTextService", "Already connected or connecting.")
            return
        }

        _connectionStatus.value = SocketStatus.CONNECTING
        val request = Request.Builder().url(webSocketUrl).build()
        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d("VoiceToTextService", "WebSocket Connected")
                _connectionStatus.value = SocketStatus.CONNECTED
                // You might want to send an initial message or configuration here
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d("VoiceToTextService", "Receiving: $text")
                _textResult.value = text
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                Log.d("VoiceToTextService", "Receiving bytes: ${bytes.hex()}")
                // Handle binary messages if your service sends them
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d("VoiceToTextService", "Closing: $code / $reason")
                _connectionStatus.value = SocketStatus.DISCONNECTED
                webSocket.close(1000, null)
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e("VoiceToTextService", "Error: ${t.message}", t)
                _connectionStatus.value = SocketStatus.ERROR(t.message ?: "Unknown error")
                // Consider attempting to reconnect here or notifying the UI
            }
        })
    }

    fun sendAudio(audioData: ByteString) {
        if (_connectionStatus.value == SocketStatus.CONNECTED) {
            webSocket?.send(audioData)
        } else {
            Log.w("VoiceToTextService", "Cannot send audio, WebSocket not connected.")
        }
    }

    fun sendText(textData: String) {
        if (_connectionStatus.value == SocketStatus.CONNECTED) {
            webSocket?.send(textData)
        } else {
            Log.w("VoiceToTextService", "Cannot send text, WebSocket not connected.")
        }
    }

    fun disconnect() {
        Log.d("VoiceToTextService", "Disconnecting WebSocket")
        webSocket?.close(1000, "Client disconnected")
        _connectionStatus.value = SocketStatus.DISCONNECTED
    }

    // Call this to clean up resources when the service is no longer needed
    fun shutdown() {
        client.dispatcher.executorService.shutdown()
    }
}

sealed class SocketStatus {
    object CONNECTING : SocketStatus()
    object CONNECTED : SocketStatus()
    object DISCONNECTED : SocketStatus()
    data class ERROR(val message: String) : SocketStatus()
}
